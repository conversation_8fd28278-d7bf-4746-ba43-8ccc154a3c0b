package cn.bztmaster.cnt.module.publicbiz.api.siteManagement;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;

import cn.bztmaster.cnt.module.publicbiz.api.siteManagement.dto.SiteManagementPageReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.siteManagement.dto.SiteManagementRespDTO;
import cn.bztmaster.cnt.module.publicbiz.api.siteManagement.dto.SiteManagementSaveReqDTO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement.vo.SiteManagementPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement.vo.SiteManagementSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.convert.siteManagement.SiteManagementConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.siteManagement.SiteManagementDO;
import cn.bztmaster.cnt.module.publicbiz.service.siteManagement.SiteManagementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 场地管理 API 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class SiteManagementApiImpl implements SiteManagementApi {

    @Resource
    private SiteManagementService siteManagementService;

    @Override
    public CommonResult<Long> createSiteManagement(SiteManagementSaveReqDTO createReqDTO) {
        SiteManagementSaveReqVO createReqVO = SiteManagementConvert.INSTANCE.convertToSaveReqVO(createReqDTO);
        return success(siteManagementService.createSiteManagement(createReqVO));
    }

    @Override
    public CommonResult<Boolean> updateSiteManagement(SiteManagementSaveReqDTO updateReqDTO) {
        SiteManagementSaveReqVO updateReqVO = SiteManagementConvert.INSTANCE.convertToSaveReqVO(updateReqDTO);
        siteManagementService.updateSiteManagement(updateReqVO);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> deleteSiteManagement(Long id) {
        siteManagementService.deleteSiteManagement(id);
        return success(true);
    }

    @Override
    public CommonResult<SiteManagementRespDTO> getSiteManagement(Long id) {
        SiteManagementDO siteManagement = siteManagementService.getSiteManagement(id);
        return success(SiteManagementConvert.INSTANCE.convertToDTO(siteManagement));
    }

    @Override
    public CommonResult<List<SiteManagementRespDTO>> getSiteManagementList(Collection<Long> ids) {
        List<SiteManagementDO> list = siteManagementService.getSiteManagementList(
                new cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement.vo.SiteManagementListReqVO());
        // TODO: 根据ids过滤
        return success(SiteManagementConvert.INSTANCE.convertToDTOList(list));
    }

    @Override
    public CommonResult<PageResult<SiteManagementRespDTO>> getSiteManagementPage(SiteManagementPageReqDTO pageReqDTO) {
        SiteManagementPageReqVO pageReqVO = SiteManagementConvert.INSTANCE.convertToPageReqDTO(pageReqDTO);
        PageResult<SiteManagementDO> pageResult = siteManagementService.getSiteManagementPage(pageReqVO);
        return success(SiteManagementConvert.INSTANCE.convertToDTOPage(pageResult));
    }

    @Override
    public CommonResult<Boolean> validateSiteManagementList(Collection<Long> ids) {
        // TODO: 实现校验逻辑
        return success(true);
    }

}
