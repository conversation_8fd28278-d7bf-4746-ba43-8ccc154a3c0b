package cn.bztmaster.cnt.module.publicbiz.service.siteManagement.impl;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement.vo.*;
import cn.bztmaster.cnt.module.publicbiz.convert.siteManagement.SiteAppointmentConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.siteManagement.SiteAppointmentDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.siteManagement.SiteManagementDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.siteManagement.SiteAppointmentMapper;
import cn.bztmaster.cnt.module.publicbiz.enums.AppointmentStatusEnum;
import cn.bztmaster.cnt.module.publicbiz.service.siteManagement.SiteAppointmentService;
import cn.bztmaster.cnt.module.publicbiz.service.siteManagement.SiteManagementService;
import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.*;

/**
 * 场地预约 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class SiteAppointmentServiceImpl implements SiteAppointmentService {

    @Resource
    private SiteAppointmentMapper siteAppointmentMapper;

    @Resource
    private SiteManagementService siteManagementService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createSiteAppointment(SiteAppointmentSaveReqVO createReqVO) {
        // 校验场地存在且可预约
        SiteManagementDO siteManagement = siteManagementService.validateSiteManagementBookable(createReqVO.getSiteId());
        
        // 校验时间冲突
        validateSiteAppointmentTimeConflict(createReqVO, null);
        
        // 校验人数不超过场地容量
        if (createReqVO.getPeopleCount() > siteManagement.getSeatTotal()) {
            throw exception(SITE_APPOINTMENT_PEOPLE_COUNT_EXCEED);
        }

        // 插入
        SiteAppointmentDO siteAppointment = SiteAppointmentConvert.INSTANCE.convert(createReqVO);
        // 设置场地名称
        siteAppointment.setSiteName(siteManagement.getName());
        // 设置默认状态
        if (siteAppointment.getStatus() == null) {
            siteAppointment.setStatus(AppointmentStatusEnum.CONFIRMED.getStatus());
        }
        siteAppointmentMapper.insert(siteAppointment);
        
        // 返回
        return siteAppointment.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSiteAppointment(SiteAppointmentSaveReqVO updateReqVO) {
        // 校验存在
        validateSiteAppointmentExists(updateReqVO.getId());
        
        // 校验场地存在且可预约
        SiteManagementDO siteManagement = siteManagementService.validateSiteManagementBookable(updateReqVO.getSiteId());
        
        // 校验时间冲突
        validateSiteAppointmentTimeConflict(updateReqVO, updateReqVO.getId());
        
        // 校验人数不超过场地容量
        if (updateReqVO.getPeopleCount() > siteManagement.getSeatTotal()) {
            throw exception(SITE_APPOINTMENT_PEOPLE_COUNT_EXCEED);
        }

        // 更新
        SiteAppointmentDO updateObj = SiteAppointmentConvert.INSTANCE.convert(updateReqVO);
        // 设置场地名称
        updateObj.setSiteName(siteManagement.getName());
        siteAppointmentMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSiteAppointment(Long id) {
        // 校验存在
        validateSiteAppointmentExists(id);
        
        // 删除
        siteAppointmentMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelSiteAppointment(Long id) {
        // 校验存在且可取消
        SiteAppointmentDO siteAppointment = validateSiteAppointmentCancellable(id);
        
        // 更新状态为已取消
        SiteAppointmentDO updateObj = new SiteAppointmentDO();
        updateObj.setId(id);
        updateObj.setStatus(AppointmentStatusEnum.CANCELLED.getStatus());
        siteAppointmentMapper.updateById(updateObj);
    }

    @Override
    public SiteAppointmentDO getSiteAppointment(Long id) {
        return siteAppointmentMapper.selectById(id);
    }

    @Override
    public PageResult<SiteAppointmentDO> getSiteAppointmentPage(SiteAppointmentPageReqVO pageReqVO) {
        return siteAppointmentMapper.selectPage(pageReqVO);
    }

    @Override
    public List<SiteAppointmentDO> getSiteAppointmentList(SiteAppointmentListReqVO listReqVO) {
        return siteAppointmentMapper.selectList(listReqVO);
    }

    @Override
    public List<SiteAppointmentDO> getSiteAppointmentListBySiteAndDate(SiteAppointmentBySiteAndDateReqVO reqVO) {
        return siteAppointmentMapper.selectListBySiteAndDate(reqVO);
    }

    @Override
    public SiteAppointmentDO validateSiteAppointmentExists(Long id) {
        if (id == null) {
            return null;
        }
        SiteAppointmentDO siteAppointment = siteAppointmentMapper.selectById(id);
        if (siteAppointment == null) {
            throw exception(SITE_APPOINTMENT_NOT_EXISTS);
        }
        return siteAppointment;
    }

    @Override
    public void validateSiteAppointmentTimeConflict(SiteAppointmentSaveReqVO createReqVO, Long excludeId) {
        List<SiteAppointmentDO> conflictAppointments = siteAppointmentMapper.selectConflictAppointments(
                createReqVO.getSiteId(), 
                createReqVO.getStartDate(), 
                createReqVO.getEndDate(),
                createReqVO.getStartTime(), 
                createReqVO.getEndTime(), 
                excludeId);
        
        if (CollUtil.isNotEmpty(conflictAppointments)) {
            throw exception(SITE_APPOINTMENT_TIME_CONFLICT);
        }
    }

    @Override
    public SiteAppointmentDO validateSiteAppointmentCancellable(Long id) {
        SiteAppointmentDO siteAppointment = validateSiteAppointmentExists(id);
        if (!AppointmentStatusEnum.isCancellable(siteAppointment.getStatus())) {
            throw exception(SITE_APPOINTMENT_NOT_CANCELLABLE);
        }
        return siteAppointment;
    }

}
