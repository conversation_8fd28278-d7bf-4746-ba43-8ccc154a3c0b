package cn.bztmaster.cnt.module.publicbiz.convert.siteManagement;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import cn.bztmaster.cnt.module.publicbiz.api.siteManagement.dto.SiteManagementPageReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.siteManagement.dto.SiteManagementRespDTO;
import cn.bztmaster.cnt.module.publicbiz.api.siteManagement.dto.SiteManagementSaveReqDTO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.siteManagement.SiteManagementDO;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 场地管理 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface SiteManagementConvert {

    SiteManagementConvert INSTANCE = Mappers.getMapper(SiteManagementConvert.class);

    // ========== DO 转换 ==========

    @Mapping(target = "seatTypes", expression = "java(convertSeatTypesFromJson(bean.getSeatTypes()))")
    SiteManagementRespVO convert(SiteManagementDO bean);

    @Mapping(target = "seatTypes", expression = "java(convertSeatTypesToJson(bean.getSeatTypes()))")
    @Mapping(target = "seatTotal", expression = "java(calculateSeatTotal(bean.getSeatTypes()))")
    @Mapping(target = "seatDetail", expression = "java(generateSeatDetail(bean.getSeatTypes()))")
    SiteManagementDO convert(SiteManagementSaveReqVO bean);

    List<SiteManagementRespVO> convertList(List<SiteManagementDO> list);

    PageResult<SiteManagementRespVO> convertPage(PageResult<SiteManagementDO> page);

    // ========== DTO 转换 ==========

    @Mapping(target = "seatTypes", expression = "java(convertSeatTypesFromJsonToDTO(bean.getSeatTypes()))")
    SiteManagementRespDTO convertToDTO(SiteManagementDO bean);

    @Mapping(target = "seatTypes", expression = "java(convertSeatTypesToJsonFromDTO(bean.getSeatTypes()))")
    @Mapping(target = "seatTotal", expression = "java(calculateSeatTotalFromDTO(bean.getSeatTypes()))")
    @Mapping(target = "seatDetail", expression = "java(generateSeatDetailFromDTO(bean.getSeatTypes()))")
    SiteManagementDO convert(SiteManagementSaveReqDTO bean);

    List<SiteManagementRespDTO> convertToDTOList(List<SiteManagementDO> list);

    PageResult<SiteManagementRespDTO> convertToDTOPage(PageResult<SiteManagementDO> page);

    // ========== VO 与 DTO 转换 ==========

    SiteManagementPageReqDTO convert(SiteManagementPageReqVO bean);

    SiteManagementSaveReqVO convert(SiteManagementSaveReqDTO bean);

    // ========== 辅助方法 ==========

    /**
     * 将JSON字符串转换为座位类型列表（VO版本）
     */
    default List<SiteManagementRespVO.SeatTypeVO> convertSeatTypesFromJson(String seatTypesJson) {
        if (StrUtil.isBlank(seatTypesJson)) {
            return null;
        }
        return JSONUtil.toList(seatTypesJson, SiteManagementRespVO.SeatTypeVO.class);
    }

    /**
     * 将JSON字符串转换为座位类型列表（DTO版本）
     */
    default List<SiteManagementRespDTO.SeatTypeDTO> convertSeatTypesFromJsonToDTO(String seatTypesJson) {
        if (StrUtil.isBlank(seatTypesJson)) {
            return null;
        }
        return JSONUtil.toList(seatTypesJson, SiteManagementRespDTO.SeatTypeDTO.class);
    }

    /**
     * 将座位类型列表转换为JSON字符串（VO版本）
     */
    default String convertSeatTypesToJson(List<SiteManagementSaveReqVO.SeatTypeVO> seatTypes) {
        if (seatTypes == null || seatTypes.isEmpty()) {
            return null;
        }
        return JSONUtil.toJsonStr(seatTypes);
    }

    /**
     * 将座位类型列表转换为JSON字符串（DTO版本）
     */
    default String convertSeatTypesToJsonFromDTO(List<SiteManagementSaveReqDTO.SeatTypeDTO> seatTypes) {
        if (seatTypes == null || seatTypes.isEmpty()) {
            return null;
        }
        return JSONUtil.toJsonStr(seatTypes);
    }

    /**
     * 计算总座位数（VO版本）
     */
    default Integer calculateSeatTotal(List<SiteManagementSaveReqVO.SeatTypeVO> seatTypes) {
        if (seatTypes == null || seatTypes.isEmpty()) {
            return 0;
        }
        return seatTypes.stream()
                .mapToInt(SiteManagementSaveReqVO.SeatTypeVO::getCount)
                .sum();
    }

    /**
     * 计算总座位数（DTO版本）
     */
    default Integer calculateSeatTotalFromDTO(List<SiteManagementSaveReqDTO.SeatTypeDTO> seatTypes) {
        if (seatTypes == null || seatTypes.isEmpty()) {
            return 0;
        }
        return seatTypes.stream()
                .mapToInt(SiteManagementSaveReqDTO.SeatTypeDTO::getCount)
                .sum();
    }

    /**
     * 生成座位详情描述（VO版本）
     */
    default String generateSeatDetail(List<SiteManagementSaveReqVO.SeatTypeVO> seatTypes) {
        if (seatTypes == null || seatTypes.isEmpty()) {
            return null;
        }
        return seatTypes.stream()
                .map(seat -> seat.getName() + ":" + seat.getCount() + "座")
                .reduce((a, b) -> a + "，" + b)
                .orElse(null);
    }

    /**
     * 生成座位详情描述（DTO版本）
     */
    default String generateSeatDetailFromDTO(List<SiteManagementSaveReqDTO.SeatTypeDTO> seatTypes) {
        if (seatTypes == null || seatTypes.isEmpty()) {
            return null;
        }
        return seatTypes.stream()
                .map(seat -> seat.getName() + ":" + seat.getCount() + "座")
                .reduce((a, b) -> a + "，" + b)
                .orElse(null);
    }

}
